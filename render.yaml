services:
  - type: web
    name: netsuite-agent-dashboard
    env: node
    plan: free
    buildCommand: npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        fromDatabase:
          name: mongodb
          property: connectionString
      - key: FRONTEND_URL
        value: https://netsuite-agent-dashboard.onrender.com
    healthCheckPath: /health

databases:
  - name: mongodb
    databaseName: netsuite_dashboard
    user: dashboard_user
