import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  ArrowDownTrayIcon,
  UsersIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  IdentificationIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import RevenueAnalytics from '../components/dashboard/RevenueAnalytics';

interface ContractRecord {
  id: string;
  customer: string;
  firstName: string;
  lastName: string;
  companyName: string;
  agentNPN: string;
  insuranceCompany: string;
  contractStatus: string;
  lastModified: string;
  class: string;
  upline: string;
  states: string;
  dataURI?: string;
}

interface CommissionRecord {
  id: string;
  agentNPN: string;
  amount: number;
  date: string;
  description: string;
  status: string;
  vendorBillId: string;
  customer: string;
  insuranceCompany: string;
  rawData?: any; // Store the full rawData for the modal
}

interface AgentSummary {
  agentNPN: string;
  agentName: string;
  totalContracts: number;
  totalCommissions: number;
  activeContracts: number;
  pendingContracts: number;
  lastActivity: string;
}

export default function AgentContractDashboard() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const npnFilter = searchParams.get('npn') || '';
  const viewParam = searchParams.get('view') || '';

  const [contracts, setContracts] = useState<ContractRecord[]>([]);
  const [commissions, setCommissions] = useState<CommissionRecord[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingPage, setIsLoadingPage] = useState(false);
  const [isLoadingCommissions, setIsLoadingCommissions] = useState(false);
  const [viewMode, setViewMode] = useState<'contracts' | 'commissions'>('contracts');
  const [allContracts, setAllContracts] = useState<ContractRecord[]>([]);

  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Modal state for commission details
  const [selectedCommission, setSelectedCommission] = useState<CommissionRecord | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Export modal state
  const [showExportModal, setShowExportModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Pagination state for contracts
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAgents, setTotalAgents] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPrevPage, setHasPrevPage] = useState(false);
  const pageSize = 12;

  // Pagination state for commissions
  const [commissionPage, setCommissionPage] = useState(1);
  const [commissionTotalPages, setCommissionTotalPages] = useState(1);
  const [commissionTotal, setCommissionTotal] = useState(0);
  const [commissionHasNext, setCommissionHasNext] = useState(false);
  const [commissionHasPrev, setCommissionHasPrev] = useState(false);
  const commissionPageSize = 12;

  // Database stats
  const [dbStats, setDbStats] = useState({
    totalAgents: 0,
    totalContractsRequested: 0,
    totalCommissions: 0,
    totalCommissionAmount: 0,
    thisMonthCommissionAmount: 0,
    lastMonthCommissionAmount: 0,
    lastCommissionSync: null as string | null,
    lastAgentSync: null as string | null,
    commissionSyncStatus: 'idle' as string,
    agentSyncStatus: 'idle' as string,
    commissionSyncRunning: false,
    agentSyncRunning: false,
    cronJobsActive: false
  });

  // Load database stats
  const loadDatabaseStats = async () => {
    try {
      const { getStats, testBackendConnection } = await import('../utils/backendApi');

      const isBackendAvailable = await testBackendConnection();
      if (isBackendAvailable) {
        const stats = await getStats();
        setDbStats({
          totalAgents: stats.totalAgents,
          totalContractsRequested: stats.totalContractsRequested,
          totalCommissions: stats.totalCommissions,
          totalCommissionAmount: stats.totalCommissionAmount,
          thisMonthCommissionAmount: stats.thisMonthCommissionAmount,
          lastMonthCommissionAmount: stats.lastMonthCommissionAmount,
          lastCommissionSync: stats.lastCommissionSync,
          lastAgentSync: stats.lastAgentSync,
          commissionSyncStatus: stats.commissionSyncStatus,
          agentSyncStatus: stats.agentSyncStatus,
          commissionSyncRunning: stats.commissionSyncRunning,
          agentSyncRunning: stats.agentSyncRunning,
          cronJobsActive: stats.cronJobsActive
        });
      }
    } catch (error) {
      console.error('Error loading database stats:', error);
    }
  };

  // Load real NetSuite data from backend with pagination and search
  const loadContractData = async (page: number = 1, search: string = '') => {
    try {
      setIsSearching(!!search);

      // Build API URL with search parameter if provided
      const baseUrl = import.meta.env.VITE_API_URL ||
        (import.meta.env.PROD ? '/api' : 'http://localhost:3001/api');
      let apiUrl = `${baseUrl}/agents?page=${page}&limit=${pageSize}`;
      if (search.trim()) {
        apiUrl += `&search=${encodeURIComponent(search.trim())}`;
      }

      // Try backend API first
      const response = await fetch(apiUrl);

      if (response.ok) {
        console.log('✅ Backend available, fetching real NetSuite data...');
        const result = await response.json();

        if (result.success) {
          // Transform the data - backend now returns rawData format
          const transformedContracts = result.data.map((item: any) => {
            // Backend now returns rawData as the primary format
            const agent: any = item;
            return {
              id: agent.id || agent['Contract ID #'] || '',
              customer: agent['Customer  '] || agent.customer || '',
              firstName: agent['First Name'] || agent.firstName || '',
              lastName: agent['Last Name'] || agent.lastName || '',
              companyName: agent['Company Name'] || agent.companyName || '',
              agentNPN: agent['Agent NPN'] || agent.agentNPN || '',
              insuranceCompany: agent['Insurance Company'] || agent.insuranceCompany || '',
              contractStatus: agent['Contract Status'] || agent.contractStatus || '',
              lastModified: agent['Last Modified'] || agent.lastModified || '',
              class: agent['Class'] || agent.class || '',
              upline: agent['Upline'] || agent.upline || '',
              states: agent['States'] || agent.states || '',
              dataURI: agent.dataURI || ''
            };
          });

          return {
            contracts: transformedContracts,
            pagination: result.pagination
          };
        }
      }

      // Fallback for specific agent or when backend is not available
      if (npnFilter) {
        const { getAgentContracts } = await import('../utils/backendApi');
        const agentData = await getAgentContracts(npnFilter);

        const transformedContracts = agentData.contracts.map(item => {
          const agent: any = item.record || item;
          return {
            id: agent.id || agent['Contract ID #'] || '',
            customer: agent['Customer  '] || agent.customer || '',
            firstName: agent['First Name'] || agent.firstName || '',
            lastName: agent['Last Name'] || agent.lastName || '',
            companyName: agent['Company Name'] || agent.companyName || '',
            agentNPN: agent['Agent NPN'] || agent.agentNPN || '',
            insuranceCompany: agent['Insurance Company'] || agent.insuranceCompany || '',
            contractStatus: agent['Contract Status'] || agent.contractStatus || '',
            lastModified: agent['Last Modified'] || agent.lastModified || '',
            class: agent['Class'] || agent.class || '',
            upline: agent['Upline'] || agent.upline || '',
            states: agent['States'] || agent.states || '',
            dataURI: agent.dataURI || ''
          };
        });

        return {
          contracts: transformedContracts,
          pagination: {
            totalAgents: agentData.totalContracts,
            totalPages: 1,
            hasNextPage: false,
            hasPrevPage: false,
            currentPage: 1
          }
        };
      } else {
        console.log('⚠️ Backend not available, falling back to static file...');
        // Fallback to static file
        const response = await fetch('/netsuite-contracts-all-records-first 1000.json');
        const data = await response.json();

        const transformedContracts: ContractRecord[] = data.page_of_records.map((item: any) => ({
          id: item.record.id,
          customer: item.record['Customer  '] || '',
          firstName: item.record['First Name'] || '',
          lastName: item.record['Last Name'] || '',
          companyName: item.record['Company Name'] || '',
          agentNPN: item.record['Agent NPN'] || '',
          insuranceCompany: item.record['Insurance Company'] || '',
          contractStatus: item.record['Contract Status'] || '',
          lastModified: item.record['Last Modified'] || '',
          class: item.record['Class'] || '',
          upline: item.record['Upline'] || '',
          states: item.record['States'] || '',
          dataURI: item.record.dataURI
        }));

        // Simulate pagination for static data
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedContracts = transformedContracts.slice(startIndex, endIndex);

        return {
          contracts: paginatedContracts,
          pagination: {
            totalAgents: transformedContracts.length,
            totalPages: Math.ceil(transformedContracts.length / pageSize),
            hasNextPage: endIndex < transformedContracts.length,
            hasPrevPage: page > 1,
            currentPage: page
          }
        };
      }
    } catch (error) {
      console.error('Error loading contract data:', error);
      return {
        contracts: [],
        pagination: {
          totalAgents: 0,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false,
          currentPage: 1
        }
      };
    }
  };

  // Load paginated commission data for commission view with search
  const loadPaginatedCommissions = async (page: number = 1, search: string = '') => {
    try {
      setIsLoadingCommissions(true);

      // Build API URL with search parameter if provided
      const baseUrl = import.meta.env.VITE_API_URL ||
        (import.meta.env.PROD ? '/api' : 'http://localhost:3001/api');
      let apiUrl = `${baseUrl}/commissions?page=${page}&limit=${commissionPageSize}&grouped=true`;
      if (search.trim()) {
        apiUrl += `&search=${encodeURIComponent(search.trim())}`;
      }

      // Fetch commissions with pagination from the API
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch commissions: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch commissions');
      }

      // Transform grouped commission data to match the expected format
      const transformedCommissions = result.data.map((group: any) => ({
        id: group._id || group.transactionNumber,
        transactionNumber: group.transactionNumber,
        agentNPN: group.agentNPN,
        amount: group.totalAmount || 0,
        totalAmount: group.totalAmount || 0,
        date: group.date,
        status: group.status,
        customer: group.customer,
        carrier: group.carrier,
        insuranceCompany: group.carrier,
        class: group.class,
        paymentType: group.paymentType,
        memo: group.memo,
        itemCount: group.itemCount || 1,
        items: group.items || [],
        // Store the first item's rawData for backward compatibility
        rawData: group.items && group.items.length > 0 ? group.items[0] : {}
      }));

      setCommissions(transformedCommissions);

      // Update pagination state
      const pagination = result.pagination;
      setCommissionTotalPages(pagination.pages);
      setCommissionTotal(pagination.total);
      setCommissionHasNext(pagination.page < pagination.pages);
      setCommissionHasPrev(pagination.page > 1);

      setIsLoadingCommissions(false);
      return transformedCommissions;
    } catch (error) {
      console.error('Error loading paginated commissions:', error);
      setIsLoadingCommissions(false);
      return [];
    }
  };

  // 🔥 NEW: Load commission data using bulk pagination (same approach as agents) - for summary stats
  const loadCommissionData = async (): Promise<CommissionRecord[]> => {
    try {
      const { getBulkCommissions, testBackendConnection, generateSampleCommissions } = await import('../utils/backendApi');

      const isBackendAvailable = await testBackendConnection();

      if (isBackendAvailable) {
        console.log('✅ Backend available, fetching commission data using bulk pagination...');

        // Fetch all commissions using pagination (same approach as agents)
        let allCommissions: any[] = [];
        let currentPage = 1;
        let hasMore = true;
        const pageSize = 50;

        while (hasMore && allCommissions.length < 1000) { // Limit to prevent infinite loops
          try {
            const commissionData = await getBulkCommissions(currentPage, pageSize);

            if (commissionData.commissions && commissionData.commissions.length > 0) {
              allCommissions = allCommissions.concat(commissionData.commissions);
              console.log(`📄 Commission page ${currentPage}: ${commissionData.commissions.length} records (Total: ${allCommissions.length})`);

              hasMore = commissionData.hasNextPage;
              currentPage++;

              // Small delay between pages
              await new Promise(resolve => setTimeout(resolve, 100));
            } else {
              hasMore = false;
            }
          } catch (pageError) {
            console.error(`Error fetching commission page ${currentPage}:`, pageError);
            hasMore = false;
          }
        }

        console.log(`🎯 Total commissions fetched: ${allCommissions.length}`);

        // Transform to expected format
        return allCommissions.map(commission => ({
          id: commission.id,
          agentNPN: commission.agentNPN,
          amount: commission.amount,
          date: commission.date,
          description: `Commission - ${commission.coverageMonth || 'N/A'}`,
          status: commission.status,
          vendorBillId: commission.vendorBillId,
          customer: commission.customer || `Customer for ${commission.agentNPN}`,
          insuranceCompany: commission.carrier || commission.insuranceCompany || 'Unknown'
        }));

      } else {
        console.log('⚠️ Backend not available, generating sample commissions...');
        // Fallback to client-side generation
        return generateSampleCommissions(allContracts);
      }
    } catch (error) {
      console.error('Error loading commission data:', error);
      // Fallback to client-side generation
      const { generateSampleCommissions } = await import('../utils/backendApi');
      return generateSampleCommissions(allContracts);
    }
  };

  // Initial data load effect - only runs on mount and when URL params change
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);

      try {
        // Load database stats first
        await loadDatabaseStats();

        // Reset pagination to page 1 when view changes
        setCurrentPage(1);
        setCommissionPage(1);

        // Determine view mode based on URL parameters
        if (viewParam === 'commissions') {
          setViewMode('commissions');
          // Load paginated commissions for commission view (page 1, no search)
          await loadPaginatedCommissions(1);
        } else {
          setViewMode('contracts');
          // Load contract data with pagination (page 1, no search)
          const contractData = await loadContractData(1);

          // Update pagination state - map API response to frontend state
          const pagination = contractData.pagination as any;
          setTotalAgents(pagination.total || 0);
          setTotalPages(pagination.pages || 1);
          setHasNextPage(pagination.page < pagination.pages);
          setHasPrevPage(false); // Always false for page 1

          // Set contracts
          setAllContracts(contractData.contracts);
          setContracts(contractData.contracts);

          // Note: Removed loadCommissionData() call to prevent unnecessary bulk API calls
          // Commission data for summary stats will be loaded only when needed
        }

        // If there's an NPN parameter, automatically show commissions for that agent
        if (npnFilter) {
          setViewMode('commissions');
          setSelectedAgent(npnFilter);
        } else {
          setSelectedAgent(null);
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setIsLoading(false);
        setIsLoadingPage(false);
      }
    };

    loadInitialData();
  }, [npnFilter, viewParam]); // Removed currentPage and commissionPage from dependencies

  // Cleanup search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Periodic stats refresh for sync status updates
  useEffect(() => {
    const interval = setInterval(() => {
      loadDatabaseStats();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string | undefined) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';

    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'submitted': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'portal set-up in progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'pending': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'paid': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string | undefined) => {
    if (!status) return <ClockIcon className="w-4 h-4" />;

    switch (status.toLowerCase()) {
      case 'active': return <CheckCircleIcon className="w-4 h-4" />;
      case 'submitted': return <DocumentTextIcon className="w-4 h-4" />;
      case 'portal set-up in progress': return <ClockIcon className="w-4 h-4" />;
      case 'pending': return <ExclamationTriangleIcon className="w-4 h-4" />;
      case 'paid': return <CheckCircleIcon className="w-4 h-4" />;
      default: return <ClockIcon className="w-4 h-4" />;
    }
  };

  const handleAgentClick = (agentNPN: string) => {
    // Navigate to the dedicated agent detail view
    navigate(`/agent/${agentNPN}`);
  };

  // Modal handlers for commission details
  const handleViewCommissionDetails = (commission: CommissionRecord) => {
    setSelectedCommission(commission);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCommission(null);
  };

  // Export modal handlers
  const handleExportClick = () => {
    setShowExportModal(true);
  };

  const handleCloseExportModal = () => {
    setShowExportModal(false);
  };

  // Export all agents without pagination
  const exportAllAgents = async () => {
    try {
      setIsExporting(true);
      console.log('🔄 Exporting all agents...');

      // Fetch all agents without pagination
      let allAgents: any[] = [];
      let currentPage = 1;
      let hasMore = true;

      while (hasMore) {
        const baseUrl = import.meta.env.VITE_API_URL ||
          (import.meta.env.PROD ? '/api' : 'http://localhost:3001/api');
        const response = await fetch(`${baseUrl}/agents?page=${currentPage}&limit=100`);
        if (!response.ok) throw new Error('Failed to fetch agents');

        const result = await response.json();
        if (result.success && result.data.length > 0) {
          allAgents = allAgents.concat(result.data);
          hasMore = result.pagination.page < result.pagination.pages;
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      console.log(`✅ Fetched ${allAgents.length} total agents for export`);

      // Create CSV content
      const headers = [
        'Contract ID',
        'First Name',
        'Last Name',
        'Customer',
        'Company Name',
        'Agent NPN',
        'Insurance Company',
        'Contract Status',
        'Class',
        'Upline',
        'States',
        'Last Modified'
      ];

      const csvContent = [
        headers.join(','),
        ...allAgents.map(agent =>
          [
            agent['Contract ID #'] || agent.id,
            agent['First Name'] || agent.firstName,
            agent['Last Name'] || agent.lastName,
            agent['Customer  '] || agent.customer,
            agent['Company Name'] || agent.companyName,
            agent['Agent NPN'] || agent.agentNPN,
            agent['Insurance Company'] || agent.insuranceCompany,
            agent['Contract Status'] || agent.contractStatus,
            agent['Class'] || agent.class,
            agent['Upline'] || agent.upline,
            agent['States'] || agent.states,
            agent['Last Modified'] || agent.lastModified
          ].map(field => `"${field || ''}"`).join(',')
        )
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-agents-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      setIsExporting(false);
      setShowExportModal(false);
    } catch (error) {
      console.error('Error exporting agents:', error);
      setIsExporting(false);
      alert('Failed to export agents. Please try again.');
    }
  };

  // Export all commissions without pagination
  const exportAllCommissions = async () => {
    try {
      setIsExporting(true);
      console.log('🔄 Exporting all commissions...');

      // Fetch all commissions without pagination
      let allCommissions: any[] = [];
      let currentPage = 1;
      let hasMore = true;

      while (hasMore) {
        const baseUrl = import.meta.env.VITE_API_URL ||
          (import.meta.env.PROD ? '/api' : 'http://localhost:3001/api');
        const response = await fetch(`${baseUrl}/commissions?page=${currentPage}&limit=100`);
        if (!response.ok) throw new Error('Failed to fetch commissions');

        const result = await response.json();
        if (result.success && result.data && result.data.length > 0) {
          allCommissions = allCommissions.concat(result.data);
          hasMore = result.pagination.page < result.pagination.pages;
          currentPage++;
        } else {
          hasMore = false;
        }
      }

      console.log(`✅ Fetched ${allCommissions.length} total commissions for export`);

      // Create CSV content
      const headers = [
        'Transaction Number',
        'Date',
        'Amount',
        'Carrier',
        'Customer Name',
        'Agent NPN',
        'Policy Number',
        'Policy State',
        'Coverage Month',
        'Effective Date',
        'Payment Type',
        'Transaction Type',
        'Status',
        'Writing Agent',
        'Writing Agent NPN',
        'Insured First Name',
        'Insured Last Name',
        'Company Name',
        'Memo'
      ];

      const csvContent = [
        headers.join(','),
        ...allCommissions.map(commission =>
          [
            commission['Transaction Number'],
            commission['Date'],
            commission['Amount'],
            commission['Carrier'],
            commission['Name'] || `${commission['First Name'] || ''} ${commission['Last Name'] || ''}`.trim(),
            commission['Agent NPN'],
            commission['Policy Number'],
            commission['Policy State'],
            commission['Coverage Month'],
            commission['Pol. Eff. Date'],
            commission['Payment Type'],
            commission['Transaction Type'],
            commission['Status'],
            commission['Writing Agent'],
            commission['Writing Agent NPN'],
            commission['Insured First Name'],
            commission['Insured Last Name'],
            commission['Company Name'],
            commission['Memo (Main)']
          ].map(field => `"${field || ''}"`).join(',')
        )
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-commissions-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      setIsExporting(false);
      setShowExportModal(false);
    } catch (error) {
      console.error('Error exporting commissions:', error);
      setIsExporting(false);
      alert('Failed to export commissions. Please try again.');
    }
  };

  // Pagination handlers for contracts
  const handleNextPage = async () => {
    if (hasNextPage && !isLoadingPage) {
      setIsLoadingPage(true);
      const newPage = currentPage + 1;
      setCurrentPage(newPage);

      try {
        const contractData = await loadContractData(newPage, searchTerm);
        const pagination = contractData.pagination as any;
        setTotalAgents(pagination.total || pagination.totalAgents || 0);
        setTotalPages(pagination.pages || pagination.totalPages || 1);
        setHasNextPage(pagination.page < pagination.pages);
        setHasPrevPage(newPage > 1);
        setAllContracts(contractData.contracts);
        setContracts(contractData.contracts);
      } catch (error) {
        console.error('Error loading next page:', error);
      } finally {
        setIsLoadingPage(false);
      }
    }
  };

  const handlePrevPage = async () => {
    if (hasPrevPage && !isLoadingPage) {
      setIsLoadingPage(true);
      const newPage = currentPage - 1;
      setCurrentPage(newPage);

      try {
        const contractData = await loadContractData(newPage, searchTerm);
        const pagination = contractData.pagination as any;
        setTotalAgents(pagination.total || pagination.totalAgents || 0);
        setTotalPages(pagination.pages || pagination.totalPages || 1);
        setHasNextPage(pagination.page < pagination.pages);
        setHasPrevPage(newPage > 1);
        setAllContracts(contractData.contracts);
        setContracts(contractData.contracts);
      } catch (error) {
        console.error('Error loading previous page:', error);
      } finally {
        setIsLoadingPage(false);
      }
    }
  };

  const handlePageClick = async (page: number) => {
    if (page !== currentPage && !isLoadingPage) {
      setIsLoadingPage(true);
      setCurrentPage(page);

      try {
        const contractData = await loadContractData(page, searchTerm);
        const pagination = contractData.pagination as any;
        setTotalAgents(pagination.total || pagination.totalAgents || 0);
        setTotalPages(pagination.pages || pagination.totalPages || 1);
        setHasNextPage(pagination.page < pagination.pages);
        setHasPrevPage(page > 1);
        setAllContracts(contractData.contracts);
        setContracts(contractData.contracts);
      } catch (error) {
        console.error('Error loading page:', error);
      } finally {
        setIsLoadingPage(false);
      }
    }
  };

  // Debounced search function
  const performSearch = async (term: string) => {
    try {
      setIsSearching(true);

      if (viewMode === 'contracts') {
        setCurrentPage(1);
        const contractData = await loadContractData(1, term);

        // Update pagination state
        const pagination = contractData.pagination as any;
        setTotalAgents(pagination.total || pagination.totalAgents || 0);
        setTotalPages(pagination.pages || pagination.totalPages || 1);
        setHasNextPage(pagination.page < pagination.pages);
        setHasPrevPage(false); // First page

        // Set contracts
        setAllContracts(contractData.contracts);
        setContracts(contractData.contracts);
      } else {
        setCommissionPage(1);
        await loadPaginatedCommissions(1, term);
      }
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Search handlers with debouncing
  const handleSearch = (term: string) => {
    setSearchTerm(term);

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const newTimeout = setTimeout(() => {
      performSearch(term);
    }, 300); // 300ms debounce

    setSearchTimeout(newTimeout);
  };

  const handleSearchClear = () => {
    setSearchTerm('');

    // Clear timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Immediately perform search with empty term
    performSearch('');
  };

  // Pagination handlers for commissions
  const handleCommissionNextPage = async () => {
    if (commissionHasNext && !isLoadingCommissions) {
      const newPage = commissionPage + 1;
      setCommissionPage(newPage);
      await loadPaginatedCommissions(newPage, searchTerm);
    }
  };

  const handleCommissionPrevPage = async () => {
    if (commissionHasPrev && !isLoadingCommissions) {
      const newPage = commissionPage - 1;
      setCommissionPage(newPage);
      await loadPaginatedCommissions(newPage, searchTerm);
    }
  };

  const handleCommissionPageClick = async (page: number) => {
    if (page !== commissionPage && !isLoadingCommissions) {
      setCommissionPage(page);
      await loadPaginatedCommissions(page, searchTerm);
    }
  };

  const exportToCSV = () => {
    const dataToExport = viewMode === 'contracts' ? contracts : commissions;
    const headers = viewMode === 'contracts'
      ? ['ID', 'Customer', 'Agent NPN', 'Insurance Company', 'Status', 'Last Modified']
      : ['ID', 'Agent NPN', 'Amount', 'Date', 'Description', 'Status'];

    const csvContent = [
      headers.join(','),
      ...dataToExport.map(item =>
        viewMode === 'contracts'
          ? [item.id, `"${(item as ContractRecord).customer}"`, (item as ContractRecord).agentNPN, `"${(item as ContractRecord).insuranceCompany}"`, `"${(item as ContractRecord).contractStatus}"`, `"${(item as ContractRecord).lastModified}"`].join(',')
          : [item.id, (item as CommissionRecord).agentNPN, (item as CommissionRecord).amount, (item as CommissionRecord).date, `"${(item as CommissionRecord).description}"`, (item as CommissionRecord).status].join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${viewMode}-data-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-[#1A364E] to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading agent data...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-[#1A364E] to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                {npnFilter
                  ? `Agent Commissions - NPN: ${npnFilter}`
                  : viewMode === 'commissions'
                    ? 'All Commissions Dashboard'
                    : 'Agent Contract Dashboard'
                }
              </h1>
            </div>

            <div className="flex gap-4">
              {/* Show view mode buttons only when not viewing specific agent */}
              {!npnFilter && (
                <>
                  <button
                    onClick={() => navigate('/')}
                    className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                      viewMode === 'contracts'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    <DocumentTextIcon className="w-5 h-5" />
                    Contracts
                  </button>

                  <button
                    onClick={() => navigate('/?view=commissions')}
                    className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                      viewMode === 'commissions'
                        ? 'bg-green-600 text-white'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    <CurrencyDollarIcon className="w-5 h-5" />
                    Commissions
                  </button>
                </>
              )}

              {/* Show back button when viewing specific agent */}
              {npnFilter && (
                <button
                  onClick={() => navigate('/')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                >
                  ← Back to All Contracts
                </button>
              )}

              <button
                onClick={() => navigate('/npn-lookup')}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <MagnifyingGlassIcon className="w-5 h-5" />
                NPN Lookup
              </button>

              <button
                onClick={handleExportClick}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <ArrowDownTrayIcon className="w-5 h-5" />
                Export CSV
              </button>
            </div>
          </div>

          {/* Summary Stats */}
          {viewMode === 'contracts' ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Agents</p>
                      {dbStats.agentSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-400"></div>
                      )}
                    </div>
                    <p className="text-3xl font-bold text-blue-400">
                      {(dbStats.totalAgents || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.agentSyncRunning ? 'Syncing...' :
                       dbStats.agentSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.agentSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <UsersIcon className="w-10 h-10 text-blue-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Contracts Requested</p>
                      {dbStats.agentSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-orange-400"></div>
                      )}
                    </div>
                    <p className="text-3xl font-bold text-orange-400">
                      {(dbStats.totalContractsRequested || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.agentSyncRunning ? 'Syncing...' :
                       dbStats.agentSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.agentSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <DocumentTextIcon className="w-10 h-10 text-orange-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Commissions</p>
                      {dbStats.commissionSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-400"></div>
                      )}
                    </div>
                    <p className="text-3xl font-bold text-green-400">
                      ${(dbStats.totalCommissionAmount || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.commissionSyncRunning ? 'Syncing...' :
                       dbStats.commissionSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.commissionSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <CurrencyDollarIcon className="w-10 h-10 text-green-400" />
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Agents</p>
                      {dbStats.agentSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-400"></div>
                      )}
                    </div>
                    <p className="text-2xl font-bold text-blue-400">
                      {(dbStats.totalAgents || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.agentSyncRunning ? 'Syncing...' :
                       dbStats.agentSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.agentSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <UsersIcon className="w-8 h-8 text-blue-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Contracts Requested</p>
                      {dbStats.agentSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-orange-400"></div>
                      )}
                    </div>
                    <p className="text-2xl font-bold text-orange-400">
                      {(dbStats.totalContractsRequested || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.agentSyncRunning ? 'Syncing...' :
                       dbStats.agentSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.agentSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <DocumentTextIcon className="w-8 h-8 text-orange-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Total Commissions</p>
                      {dbStats.commissionSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-400"></div>
                      )}
                    </div>
                    <p className="text-2xl font-bold text-green-400">
                      ${(dbStats.totalCommissionAmount || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.commissionSyncRunning ? 'Syncing...' :
                       dbStats.commissionSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.commissionSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-green-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">This Month</p>
                      {dbStats.commissionSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-400"></div>
                      )}
                    </div>
                    <p className="text-2xl font-bold text-purple-400">
                      ${(dbStats.thisMonthCommissionAmount || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.commissionSyncRunning ? 'Syncing...' :
                       dbStats.commissionSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.commissionSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <CalendarIcon className="w-8 h-8 text-purple-400" />
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 relative">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-300 text-sm">Last Month</p>
                      {dbStats.commissionSyncRunning && (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-400"></div>
                      )}
                    </div>
                    <p className="text-2xl font-bold text-indigo-400">
                      ${(dbStats.lastMonthCommissionAmount || 0).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {dbStats.commissionSyncRunning ? 'Syncing...' :
                       dbStats.commissionSyncStatus === 'completed' ? 'Up to date' :
                       dbStats.commissionSyncStatus === 'failed' ? 'Sync failed' : 'Idle'}
                    </p>
                  </div>
                  <ClockIcon className="w-8 h-8 text-indigo-400" />
                </div>
              </div>
            </div>
          )}

          {/* Search Bar */}
          {!npnFilter && (
            <div className="mb-6">
              <form
                onSubmit={(e) => {
                  e.preventDefault(); // Prevent form submission and page reload
                  performSearch(searchTerm);
                }}
                className="relative max-w-md"
              >
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    e.preventDefault();
                    handleSearch(e.target.value);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault(); // Prevent form submission
                      performSearch(searchTerm);
                    }
                  }}
                  placeholder={viewMode === 'contracts' ? 'Search agents by name, NPN, or company...' : 'Search commissions by agent NPN, carrier, or policy...'}
                  className="block w-full pl-10 pr-10 py-3 border border-white/20 rounded-lg bg-white/10 backdrop-blur-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchTerm && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      handleSearchClear();
                    }}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </form>
              {searchTerm && (
                <div className="mt-2 text-sm text-gray-400">
                  {isSearching ? 'Searching...' : `Showing results for "${searchTerm}"`}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Revenue Analytics */}
        <div className="mb-8">
          <RevenueAnalytics npn={npnFilter} isAgencyView={!npnFilter} />
        </div>

        {/* Contract Cards */}
        {viewMode === 'contracts' && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contracts.map((contract, index) => (
                <div
                  key={`${contract.id}-${contract.agentNPN}-${index}`}
                  className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-200 cursor-pointer"
                  onClick={() => handleAgentClick(contract.agentNPN)}
                >
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-2">
                      <IdentificationIcon className="w-5 h-5 text-blue-400" />
                      <span className="text-blue-400 font-semibold">#{contract.id}</span>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium border flex items-center gap-1 ${getStatusColor(contract.contractStatus)}`}>
                      {getStatusIcon(contract.contractStatus)}
                      {contract.contractStatus || 'Unknown'}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <h3 className="text-white font-semibold text-lg mb-1">
                        {contract.firstName} {contract.lastName}
                      </h3>
                      <p className="text-gray-300 text-sm">{contract.customer}</p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <IdentificationIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300">NPN:</span>
                        <span className="text-white font-medium">{contract.agentNPN}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm">
                        <BuildingOfficeIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300 truncate">{contract.insuranceCompany}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm">
                        <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300">Class:</span>
                        <span className="text-white">{contract.class}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm">
                        <UsersIcon className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-300 truncate">{contract.upline}</span>
                      </div>
                    </div>

                    <div className="pt-3 border-t border-white/10">
                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <ClockIcon className="w-3 h-3" />
                        Last Modified: {contract.lastModified}
                      </div>
                    </div>

                    <div className="flex items-center justify-center pt-2">
                      <button className="flex items-center gap-1 text-blue-400 hover:text-blue-300 text-sm font-medium">
                        <EyeIcon className="w-4 h-4" />
                        View
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>


          </>
        )}

        {/* Commission Cards */}
        {viewMode === 'commissions' && (
          <div>
            {isLoadingCommissions ? (
              <div className="text-center py-12">
                <div className="text-white text-xl">Loading commissions...</div>
              </div>
            ) : (
              <>
                {/* Mobile Commission Cards */}
                <div className="block md:hidden space-y-4">
                  {commissions
                    .filter(commission => !selectedAgent || commission.agentNPN === selectedAgent)
                    .map((commission, index) => (
                    <div
                      key={`${commission.id}-${commission.vendorBillId}-${index}`}
                      className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-200"
                    >
                      {/* Header with amount and status */}
                      <div className="flex justify-between items-start mb-3">
                        <div className="text-green-400 font-bold text-xl">
                          ${commission.amount.toLocaleString()}
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium border flex items-center gap-1 ${getStatusColor(commission.status)}`}>
                          {getStatusIcon(commission.status)}
                          {commission.status}
                        </div>
                      </div>

                      {/* Date and Transaction Number */}
                      <div className="mb-3">
                        <div className="text-gray-300 text-sm">{commission.date}</div>
                        <div className="text-gray-400 text-xs">{commission.vendorBillId}</div>
                      </div>

                      {/* Key Details */}
                      <div className="space-y-2 mb-4">
                        <div className="text-sm">
                          <span className="text-gray-400">Carrier:</span>
                          <span className="text-white ml-2">{commission.insuranceCompany}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-400">Customer:</span>
                          <span className="text-white ml-2">{commission.customer}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-400">Policy:</span>
                          <span className="text-white ml-2">{commission.rawData?.['Policy Number'] || 'N/A'}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-400">Coverage Month:</span>
                          <span className="text-white ml-2">{commission.rawData?.['Coverage Month'] || 'N/A'}</span>
                        </div>
                      </div>

                      {/* View Details Button */}
                      <div className="flex justify-center">
                        <button
                          onClick={() => handleViewCommissionDetails(commission)}
                          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium"
                        >
                          <EyeIcon className="w-4 h-4" />
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Commission Table */}
                <div className="hidden md:block overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-white/20">
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Transaction</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Date</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Customer</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Agent NPN</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Carrier</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Class</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Payment Type</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Memo</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Status</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Amount</th>
                        <th className="text-left py-3 px-4 text-gray-300 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commissions
                        .filter(commission => !selectedAgent || commission.agentNPN === selectedAgent)
                        .map((commission, index) => (
                        <tr key={`${commission.transactionNumber || commission.id}-${index}`} className="border-b border-white/10 hover:bg-white/5">
                          <td className="py-3 px-4 text-white font-medium">{commission.transactionNumber || commission.vendorBillId}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.date}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.customer}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.agentNPN}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.carrier || commission.insuranceCompany}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.class || 'N/A'}</td>
                          <td className="py-3 px-4 text-gray-300">{commission.paymentType || 'N/A'}</td>
                          <td className="py-3 px-4 text-gray-300 max-w-32 truncate" title={commission.memo}>{commission.memo || 'N/A'}</td>
                          <td className="py-3 px-4">
                            <div className={`px-2 py-1 rounded-full text-xs font-medium border flex items-center gap-1 w-fit ${getStatusColor(commission.status)}`}>
                              {getStatusIcon(commission.status)}
                              {commission.status}
                            </div>
                          </td>
                          <td className="py-3 px-4 text-green-400 font-semibold">${(commission.totalAmount || commission.amount || 0).toLocaleString()}</td>
                          <td className="py-3 px-4">
                            <button
                              onClick={() => handleViewCommissionDetails(commission)}
                              className="flex items-center gap-1 text-blue-400 hover:text-blue-300 text-sm font-medium"
                            >
                              <EyeIcon className="w-4 h-4" />
                              View
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Commission Pagination Controls */}
                {!npnFilter && commissionTotalPages > 1 && (
                  <div className="mt-8 flex justify-center items-center gap-4">
                    <button
                      onClick={handleCommissionPrevPage}
                      disabled={!commissionHasPrev || isLoadingCommissions}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                        commissionHasPrev && !isLoadingCommissions
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <ChevronLeftIcon className="w-5 h-5" />
                      {isLoadingCommissions ? 'Loading...' : 'Previous'}
                    </button>

                    <div className="flex items-center gap-2">
                      {/* Page Numbers */}
                      {Array.from({ length: Math.min(5, commissionTotalPages) }, (_, i) => {
                        const pageNum = Math.max(1, Math.min(commissionTotalPages - 4, commissionPage - 2)) + i;
                        if (pageNum > commissionTotalPages) return null;

                        return (
                          <button
                            key={pageNum}
                            onClick={() => handleCommissionPageClick(pageNum)}
                            disabled={isLoadingCommissions}
                            className={`w-10 h-10 rounded-lg transition-colors ${
                              pageNum === commissionPage
                                ? 'bg-green-600 text-white'
                                : isLoadingCommissions
                                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                                : 'bg-white/10 text-gray-300 hover:bg-white/20'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={handleCommissionNextPage}
                      disabled={!commissionHasNext || isLoadingCommissions}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                        commissionHasNext && !isLoadingCommissions
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {isLoadingCommissions ? 'Loading...' : 'Next'}
                      <ChevronRightIcon className="w-5 h-5" />
                    </button>
                  </div>
                )}

                {/* Commission Pagination Info */}
                {!npnFilter && (
                  <div className="mt-4 text-center text-gray-400 text-sm">
                    Showing page {commissionPage} of {commissionTotalPages} ({(commissionTotal || 0).toLocaleString()} {searchTerm ? 'matching' : 'total'} commissions{searchTerm ? ` for "${searchTerm}"` : ''})
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* Pagination Controls */}
        {viewMode === 'contracts' && !npnFilter && totalPages > 1 && (
          <div className="mt-8 flex justify-center items-center gap-4">
            <button
              onClick={handlePrevPage}
              disabled={!hasPrevPage || isLoadingPage}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                hasPrevPage && !isLoadingPage
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
              }`}
            >
              <ChevronLeftIcon className="w-5 h-5" />
              {isLoadingPage ? 'Loading...' : 'Previous'}
            </button>

            <div className="flex items-center gap-2">
              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) return null;

                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageClick(pageNum)}
                    disabled={isLoadingPage}
                    className={`w-10 h-10 rounded-lg transition-colors ${
                      pageNum === currentPage
                        ? 'bg-blue-600 text-white'
                        : isLoadingPage
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
