import { useState, useEffect } from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { mockApi } from '../../utils/api';
import {
  formatDate,
  formatCurrency,
  groupByVendorBill,
  groupByCoverageMonth,
  calculateTotalCommission,
  downloadCSV
} from '../../utils/formatters';
import {
  ArrowDownTrayIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';


// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

interface CommissionDataProps {
  npn?: string;
  isAgencyView?: boolean;
}

export default function CommissionData({ npn, isAgencyView = false }: CommissionDataProps) {
  const [commissionData, setCommissionData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [groupBy, setGroupBy] = useState<'vendor' | 'month'>('vendor');
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  const [lastUpdated, setLastUpdated] = useState<string>('');

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        // Option 1: Use the mock API for development
        const data = await mockApi.getCommissionData(npn);
        setCommissionData(data);

        // Option 2: Use the Celigo API for production
        // Uncomment the following code to use the Celigo API
        /*
        const commissionData = await fetchCommissionData();
        const filteredData = filterByNPN(commissionData, npn);
        const formattedData = formatCommissionData(filteredData);
        setCommissionData(formattedData);
        */

        // Initialize all groups as expanded
        const groups = groupBy === 'vendor'
          ? Object.keys(groupByVendorBill(commissionData))
          : Object.keys(groupByCoverageMonth(commissionData));

        const initialExpandedState = groups.reduce((acc, group) => {
          acc[group] = true;
          return acc;
        }, {} as Record<string, boolean>);

        setExpandedGroups(initialExpandedState);
        setLastUpdated(new Date().toLocaleString());
      } catch (error) {
        console.error('Error fetching commission data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [npn, groupBy]);

  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  const handleExportCSV = () => {
    downloadCSV(commissionData, `commission-data-${npn || 'all'}-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const groupedData = groupBy === 'vendor'
    ? groupByVendorBill(commissionData)
    : groupByCoverageMonth(commissionData);

  // Prepare chart data
  const chartData = {
    labels: Object.keys(groupByCoverageMonth(commissionData)).map(month => {
      const date = new Date(month);
      return `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
    }),
    datasets: [
      {
        label: 'Commission Amount',
        data: Object.entries(groupByCoverageMonth(commissionData)).map(([_, items]) =>
          calculateTotalCommission(items)
        ),
        backgroundColor: 'rgba(139, 92, 246, 0.6)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Poppins',
          size: 13,
        },
        bodyFont: {
          family: 'Poppins',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return formatCurrency(context.raw);
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
          callback: function(value: any) {
            return formatCurrency(value);
          }
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
  };

  const totalCommission = calculateTotalCommission(commissionData);

  return (
    <Card className="overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b border-gray-700">
        <h2 className="text-lg font-semibold text-white">Commission Data</h2>
        <div className="flex space-x-2">
          <div className="flex rounded-md overflow-hidden">
            <button
              className={`px-3 py-1 text-xs font-medium ${
                groupBy === 'vendor'
                  ? 'bg-secondary-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150 flex items-center`}
              onClick={() => setGroupBy('vendor')}
            >
              <DocumentTextIcon className="h-3 w-3 mr-1" />
              By Statement
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium ${
                groupBy === 'month'
                  ? 'bg-secondary-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150 flex items-center`}
              onClick={() => setGroupBy('month')}
            >
              <CalendarIcon className="h-3 w-3 mr-1" />
              By Month
            </button>
          </div>
          <Button
            variant="glass"
            size="sm"
            onClick={handleExportCSV}
            icon={<ArrowDownTrayIcon className="h-4 w-4" />}
          >
            Export CSV
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="p-8 flex justify-center">
          <div className="animate-pulse flex space-x-4 items-center">
            <div className="h-12 w-12 rounded-full bg-secondary-500/20"></div>
            <div className="space-y-2">
              <div className="h-4 w-36 bg-secondary-500/20 rounded"></div>
              <div className="h-4 w-24 bg-secondary-500/20 rounded"></div>
            </div>
          </div>
        </div>
      ) : commissionData.length === 0 ? (
        <div className="p-8 text-center text-gray-400">
          <p>No commission data available.</p>
        </div>
      ) : (
        <div>
          <div className="p-4 bg-gray-800 border-b border-gray-700">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm font-medium text-gray-300">Total Commission</h3>
                <p className="text-2xl font-bold text-white">{formatCurrency(totalCommission)}</p>
              </div>
              <div className="h-40 w-full max-w-md">
                <Bar data={chartData} options={chartOptions as any} />
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            {Object.entries(groupedData).map(([groupId, items]) => (
              <div key={groupId} className="border-b border-gray-700 last:border-b-0">
                <div
                  className="flex justify-between items-center p-4 bg-gray-800 cursor-pointer hover:bg-gray-750 transition-colors duration-150"
                  onClick={() => toggleGroup(groupId)}
                >
                  <div className="flex items-center">
                    {expandedGroups[groupId] ? (
                      <ChevronUpIcon className="h-5 w-5 text-gray-400 mr-2" />
                    ) : (
                      <ChevronDownIcon className="h-5 w-5 text-gray-400 mr-2" />
                    )}
                    <div>
                      <h3 className="text-sm font-medium text-white">
                        {groupBy === 'vendor' ? `Statement: ${groupId}` : formatDate(groupId)}
                      </h3>
                      <p className="text-xs text-gray-400">
                        {items.length} {items.length === 1 ? 'entry' : 'entries'} •
                        {' '}{formatCurrency(calculateTotalCommission(items))}
                      </p>
                    </div>
                  </div>
                </div>

                {expandedGroups[groupId] && (
                  <table className="min-w-full divide-y divide-gray-700">
                    <thead className="bg-gray-900">
                      <tr>
                        {isAgencyView && (
                          <>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                              Agent NPN
                            </th>
                          </>
                        )}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          {groupBy === 'vendor' ? 'Coverage Month' : 'Statement'}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Insured
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-gray-900 divide-y divide-gray-800">
                      {items.map((item, index) => (
                        <tr key={index} className="hover:bg-gray-800 transition-colors duration-150">
                          {isAgencyView && (
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {item['Agent NPN']}
                            </td>
                          )}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {groupBy === 'vendor' ? formatDate(item['Coverage Month']) : item['Vendor Bill']}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {item['Insured First Name']} {item['Insured Last Name']}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                            {formatCurrency(item['Amount'])}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="p-3 bg-gray-800 text-xs text-gray-400 border-t border-gray-700">
        Last updated: {lastUpdated}
      </div>
    </Card>
  );
}
