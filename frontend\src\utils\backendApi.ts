/**
 * Backend API utilities for NetSuite Agent Dashboard
 */

import { requestDeduplicator, createRequestKey } from './requestDeduplication';

const API_BASE_URL = import.meta.env.VITE_API_URL ||
  (import.meta.env.PROD ? '/api' : 'http://localhost:3001/api');

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: string;
}

interface ContractRecord {
  id: string;
  customer: string;
  firstName: string;
  lastName: string;
  companyName: string;
  agentNPN: string;
  insuranceCompany: string;
  contractStatus: string;
  lastModified: string;
  class: string;
  upline: string;
  states: string;
  dataURI?: string;
}

interface CommissionRecord {
  id: string;
  agentNPN: string;
  amount: number;
  date: string;
  description: string;
  status: string;
  vendorBillId: string;
  customer: string;
  insuranceCompany: string;
}

interface ContractData {
  page: number;
  pageSize: number;
  totalResults: number;
  hasMore: boolean;
  offset: number;
  count: number;
  nextPage: number | null;
  records: any[];
  page_of_records: Array<{ record: ContractRecord }>;
}

interface AgentData {
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface CommissionData {
  totalCommissions: number;
  commissions: CommissionRecord[];
  timestamp: string;
}

/**
 * Generic API request function with deduplication
 */
async function apiRequest<T>(endpoint: string, method: string = 'GET'): Promise<T> {
  const requestKey = createRequestKey(endpoint, { method });

  return requestDeduplicator.execute(requestKey, async () => {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<T> = await response.json();

      if (!result.success) {
        throw new Error(result.message || result.error || 'API request failed');
      }

      return result.data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  });
}

/**
 * Test backend connection
 */
export async function testBackendConnection(): Promise<boolean> {
  const requestKey = createRequestKey('/health');

  return requestDeduplicator.execute(requestKey, async () => {
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
      return response.ok;
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  });
}

/**
 * Test NetSuite connection through backend
 */
export async function testNetSuiteConnection(): Promise<boolean> {
  try {
    const result = await apiRequest<{ success: boolean; message: string }>('/test-connection');
    return result.success;
  } catch (error) {
    console.error('NetSuite connection test failed:', error);
    return false;
  }
}

/**
 * Get contract records with pagination
 */
export async function getContracts(page: number = 1, limit: number = 20, npn?: string): Promise<ContractData> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (npn) {
    params.append('npn', npn);
  }

  return apiRequest<ContractData>(`/contracts?${params.toString()}`);
}

/**
 * Get agents in batches (max 50) - sorted by Last Modified
 */
export async function getAgents(page: number = 1, limit: number = 50): Promise<AgentData> {
  // Enforce max batch size of 50
  const batchLimit = Math.min(limit, 50);

  const params = new URLSearchParams({
    page: page.toString(),
    limit: batchLimit.toString(),
  });

  // The backend returns the data in the correct format already
  return apiRequest<AgentData>(`/agents?${params.toString()}`);
}

/**
 * Get recently updated agents - PRIORITY: Most recent Last Modified first
 */
export async function getRecentAgents(limit: number = 12): Promise<AgentData> {
  const params = new URLSearchParams({
    page: '1',
    limit: limit.toString(),
  });

  // Use the regular agents endpoint since it already sorts by most recent
  return apiRequest<AgentData>(`/agents?${params.toString()}`);
}

/**
 * Get agent by NPN
 */
export async function getAgentByNPN(npn: string): Promise<ContractRecord> {
  return apiRequest(`/agents/${npn}`);
}

/**
 * Get contracts for a specific agent
 */
export async function getAgentContracts(npn: string, page: number = 1, limit: number = 20): Promise<{
  agentNPN: string;
  totalContracts: number;
  contracts: Array<{ record: ContractRecord }>;
  timestamp: string;
}> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  return apiRequest(`/agents/${npn}/contracts?${params.toString()}`);
}

/**
 * Get commission data
 */
export async function getCommissions(npn?: string): Promise<CommissionData> {
  const params = npn ? `?npn=${npn}` : '';
  return apiRequest<CommissionData>(`/commissions${params}`);
}

/**
 * Get aggregated chart data for Revenue Analytics
 */
export async function getRevenueAnalyticsData(npn?: string, viewType: 'Weekly' | 'Monthly' = 'Monthly'): Promise<{
  barChart: {
    labels: string[];
    currentYear: number[];
    previousYear: number[];
    currentYearLabel: string;
    previousYearLabel: string;
  };
  pieChart: {
    labels: string[];
    data: number[];
    agentDetails: Array<{
      agentNPN: string;
      agentName: string;
      totalCommissions: number;
      commissionCount: number;
    }>;
  };
  metadata: {
    totalAgents: number;
    totalCommissions: number;
    dataRange: {
      currentYear: number;
      previousYear: number;
    };
  };
}> {
  const params = new URLSearchParams();
  if (npn) params.append('npn', npn);
  if (viewType) params.append('viewType', viewType);
  const queryString = params.toString() ? `?${params.toString()}` : '';
  return await apiRequest<any>(`/charts/revenue-analytics${queryString}`);
}

/**
 * 🔥 NEW: Get commission records with pagination (same approach as agents)
 * This replaces the per-agent commission fetching for better performance
 */
export async function getBulkCommissions(page: number = 1, limit: number = 50): Promise<{
  page: number;
  limit: number;
  totalCommissions: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  commissions: Array<{
    id: string;
    agentNPN: string;
    writingAgentNPN: string;
    vendorBillId: string;
    amount: number;
    date: string;
    coverageMonth: string;
    carrier: string;
    policyNumber: string;
    policyType: string;
    upline: string;
    paymentType: string;
    memo: string;
    status: string;
    dataURI: string;
  }>;
  source: string;
  timestamp: string;
}> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  return apiRequest(`/commissions/bulk?${params.toString()}`);
}

/**
 * Get static JSON file (fallback)
 */
export async function getStaticFile(filename: string): Promise<any> {
  return apiRequest(`/static/${filename}`);
}

/**
 * Transform backend data to frontend format
 */
export function transformContractData(backendData: ContractData): {
  exportInfo: {
    totalRecords: number;
    exportDate: string;
    recordType: string;
    sortedBy: string;
    source: string;
  };
  page_of_records: Array<{ record: ContractRecord }>;
} {
  return {
    exportInfo: {
      totalRecords: backendData.totalResults,
      exportDate: new Date().toISOString(),
      recordType: 'customrecord1138',
      sortedBy: 'Date Last Modified (most recent first)',
      source: 'NetSuite REST API - Backend Integration'
    },
    page_of_records: backendData.page_of_records
  };
}

/**
 * Generate sample commission data based on contracts (client-side fallback)
 */
export function generateSampleCommissions(contracts: ContractRecord[]): CommissionRecord[] {
  const uniqueAgents = Array.from(new Set(contracts.map(c => c.agentNPN)));
  const commissions: CommissionRecord[] = [];

  uniqueAgents.slice(0, 10).forEach((agentNPN, index) => {
    const agentContracts = contracts.filter(c => c.agentNPN === agentNPN);
    const firstContract = agentContracts[0];

    if (firstContract) {
      // Generate 1-3 commissions per agent
      const numCommissions = Math.floor(Math.random() * 3) + 1;

      for (let i = 0; i < numCommissions; i++) {
        const amount = Math.floor(Math.random() * 3000) + 500; // $500-$3500
        const statuses = ['Paid', 'Pending', 'Processing'];
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        commissions.push({
          id: `VB${String(index * 10 + i + 1).padStart(3, '0')}`,
          agentNPN: agentNPN,
          amount: amount,
          date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          description: `${firstContract.class} Commission - ${new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`,
          status: status,
          vendorBillId: `VB-2025-${String(index * 10 + i + 1).padStart(3, '0')}`,
          customer: firstContract.firstName + ' ' + firstContract.lastName,
          insuranceCompany: firstContract.insuranceCompany
        });
      }
    }
  });

  return commissions;
}

/**
 * Get unlimited sync status and progress
 */
export async function getSyncStatus(): Promise<any> {
  return apiRequest<any>('/sync/status');
}

/**
 * Force refresh all data (agents and commissions)
 */
export async function forceRefresh(): Promise<any> {
  return apiRequest<any>('/refresh', 'POST');
}

/**
 * Get database stats
 */
export async function getStats(): Promise<{
  totalAgents: number;
  totalContractsRequested: number;
  totalCommissions: number;
  totalCommissionAmount: number;
  thisMonthCommissionAmount: number;
  lastMonthCommissionAmount: number;
  lastCommissionSync: string | null;
  lastAgentSync: string | null;
  commissionSyncStatus: string;
  agentSyncStatus: string;
  commissionSyncRunning: boolean;
  agentSyncRunning: boolean;
  cronJobsActive: boolean;
}> {
  return apiRequest('/stats');
}

export type { ContractRecord, CommissionRecord, ContractData, AgentData, CommissionData };
