/**
 * API Routes for Celigo Integration
 * Provides endpoints for frontend to access commission and agent data
 */

import express from 'express';
import Commission from '../models/Commission.js';
import Agent from '../models/Agent.js';
import mongoService from '../services/mongoService.js';
import syncService from '../services/syncService.js';


const router = express.Router();




/**
 * Get all agents with pagination and filtering
 */
router.get('/agents', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      contractStatus,
      insuranceCompany,
      search,
      showAll = 'false' // New parameter to show all agents (for admin purposes)
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    const query = {};

    // Default to active agents only unless explicitly requested otherwise
    if (showAll !== 'true') {
      query.contractStatus = new RegExp('active', 'i');
    } else if (contractStatus) {
      query.contractStatus = new RegExp(contractStatus, 'i');
    }

    if (insuranceCompany) {
      query.insuranceCompany = new RegExp(insuranceCompany, 'i');
    }

    if (search) {
      query.$text = { $search: search };
    }

    // Execute query with pagination
    const [agents, total] = await Promise.all([
      Agent.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Agent.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: agents.map(agent => agent.rawData),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching agents:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agents',
      message: error.message
    });
  }
});

/**
 * Get agent by NPN
 */
router.get('/agents/:npn', async (req, res) => {
  try {
    const { npn } = req.params;

    const agent = await Agent.findOne({ agentNPN: npn }).lean();

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: 'Agent not found'
      });
    }

    res.json({
      success: true,
      data: agent.rawData
    });

  } catch (error) {
    console.error('Error fetching agent:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agent',
      message: error.message
    });
  }
});

/**
 * Get commissions for specific agent
 */
router.get('/agents/:npn/commissions', async (req, res) => {
  try {
    const { npn } = req.params;
    const {
      page = 1,
      limit = 10
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [commissions, total, aggregateData] = await Promise.all([
      Commission.find({ agentNPN: npn })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Commission.countDocuments({ agentNPN: npn }),
      Commission.aggregate([
        { $match: { agentNPN: npn } },
        {
          $group: {
            _id: null,
            totalAmount: {
              $sum: {
                $toDouble: {
                  $ifNull: [
                    { $toDouble: "$rawData.Amount" },
                    0
                  ]
                }
              }
            },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    const stats = aggregateData[0] || { totalAmount: 0, count: 0 };

    res.json({
      success: true,
      data: commissions.map(commission => commission.rawData),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      aggregates: {
        totalAmount: stats.totalAmount,
        count: stats.count,
        averageAmount: stats.count > 0 ? stats.totalAmount / stats.count : 0
      }
    });

  } catch (error) {
    console.error('Error fetching agent commissions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agent commissions',
      message: error.message
    });
  }
});

/**
 * Get ALL commissions for specific agent (no pagination) - for export
 */
router.get('/agents/:npn/commissions/all', async (req, res) => {
  try {
    const { npn } = req.params;

    const commissions = await Commission.find({ agentNPN: npn })
      .sort({ createdAt: -1 })
      .lean();

    res.json({
      success: true,
      data: commissions.map(commission => commission.rawData)
    });

  } catch (error) {
    console.error('Error fetching all agent commissions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch all agent commissions',
      message: error.message
    });
  }
});

/**
 * Get all commissions with pagination
 */
router.get('/commissions', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      agentNPN,
      search,
      grouped = 'true' // Default to grouped view
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    let query = {};
    if (agentNPN) {
      query.agentNPN = agentNPN;
    }
    if (search) {
      query.$or = [
        { agentNPN: { $regex: search, $options: 'i' } },
        { 'rawData.Carrier': { $regex: search, $options: 'i' } },
        { 'rawData.Name': { $regex: search, $options: 'i' } },
        { 'rawData.Transaction Number': { $regex: search, $options: 'i' } }
      ];
    }

    if (grouped === 'true') {
      // Group by transaction number and aggregate
      const pipeline = [
        { $match: query },
        {
          $group: {
            _id: "$rawData.Transaction Number",
            transactionNumber: { $first: "$rawData.Transaction Number" },
            date: { $first: "$rawData.Date" },
            customer: { $first: "$rawData.Name" },
            agentNPN: { $first: "$rawData.Agent NPN" },
            carrier: { $first: "$rawData.Carrier" },
            class: { $first: "$rawData.Class" },
            paymentType: { $first: "$rawData.Payment Type" },
            memo: { $first: "$rawData.Memo (Main)" },
            status: { $first: "$rawData.Status" },
            totalAmount: {
              $sum: {
                $toDouble: {
                  $ifNull: ["$rawData.Amount", "0"]
                }
              }
            },
            itemCount: { $sum: 1 },
            items: { $push: "$rawData" }
          }
        },
        { $sort: { date: -1 } },
        { $skip: skip },
        { $limit: parseInt(limit) }
      ];

      const [groupedCommissions, totalGroups] = await Promise.all([
        Commission.aggregate(pipeline),
        Commission.aggregate([
          { $match: query },
          {
            $group: {
              _id: "$rawData.Transaction Number"
            }
          },
          { $count: "total" }
        ])
      ]);

      const total = totalGroups.length > 0 ? totalGroups[0].total : 0;

      res.json({
        success: true,
        data: groupedCommissions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } else {
      // Original ungrouped view
      const [commissions, total] = await Promise.all([
        Commission.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .lean(),
        Commission.countDocuments(query)
      ]);

      res.json({
        success: true,
        data: commissions.map(commission => commission.rawData),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    }

  } catch (error) {
    console.error('Error fetching commissions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch commissions',
      message: error.message
    });
  }
});

/**
 * Get dashboard data for agent (agent + commissions)
 */
router.get('/dashboard/:npn', async (req, res) => {
  try {
    const { npn } = req.params;

    const [agent, commissions] = await Promise.all([
      Agent.findOne({ agentNPN: npn }).lean(),
      Commission.find({ agentNPN: npn })
        .sort({ createdAt: -1 })
        .limit(10)
        .lean()
    ]);

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: 'Agent not found'
      });
    }

    res.json({
      success: true,
      data: {
        agent: agent.rawData,
        commissions: commissions.map(commission => commission.rawData)
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard data',
      message: error.message
    });
  }
});

/**
 * Trigger manual sync
 */
router.post('/sync', async (req, res) => {
  try {
    const result = await syncService.triggerManualSync();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Manual sync failed',
      message: error.message
    });
  }
});

/**
 * Get sync status and database stats
 */
router.get('/stats', async (req, res) => {
  try {
    const [dbStats, syncStatus] = await Promise.all([
      mongoService.getStats(),
      syncService.getSyncStatus()
    ]);

    res.json({
      success: true,
      data: {
        ...dbStats,
        ...syncStatus
      }
    });

  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch stats',
      message: error.message
    });
  }
});

/**
 * Get aggregated chart data for Revenue Analytics
 */
router.get('/charts/revenue-analytics', async (req, res) => {
  try {
    const { npn } = req.query;

    // Build base query for filtering by agent if specified
    const baseQuery = npn ? { agentNPN: npn } : {};

    // Get monthly commission data based on "Last Modified" date from contracts
    const monthlyData = await Agent.aggregate([
      { $match: baseQuery },
      {
        $addFields: {
          lastModifiedDate: {
            $dateFromString: {
              dateString: "$rawData.Last Modified",
              format: "%m/%d/%Y %H:%M %p",
              onError: null
            }
          }
        }
      },
      { $match: { lastModifiedDate: { $ne: null } } },
      {
        $group: {
          _id: {
            year: { $year: "$lastModifiedDate" },
            month: { $month: "$lastModifiedDate" }
          },
          count: { $sum: 1 },
          agents: { $addToSet: "$agentNPN" }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Get agent commission totals for pie chart (regardless of contract status)
    const agentCommissions = await Commission.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: "$agentNPN",
          totalCommissions: {
            $sum: {
              $toDouble: {
                $ifNull: [
                  { $toDouble: "$rawData.Amount" },
                  0
                ]
              }
            }
          },
          commissionCount: { $sum: 1 }
        }
      },
      { $sort: { totalCommissions: -1 } },
      { $limit: 10 } // Top 10 agents by commission
    ]);

    // Get agent names for the pie chart
    const agentNPNs = agentCommissions.map(item => item._id);
    const agentDetails = await Agent.aggregate([
      { $match: { agentNPN: { $in: agentNPNs } } },
      {
        $group: {
          _id: "$agentNPN",
          firstName: { $first: "$rawData.First Name" },
          lastName: { $first: "$rawData.Last Name" }
        }
      }
    ]);

    // Create agent name lookup
    const agentNameMap = {};
    agentDetails.forEach(agent => {
      agentNameMap[agent._id] = `${agent.firstName || ''} ${agent.lastName || ''}`.trim() || agent._id;
    });

    // Format monthly data for bar chart
    const currentYear = new Date().getFullYear();
    const previousYear = currentYear - 1;

    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const monthlyChartData = monthNames.map((monthName, index) => {
      const monthNumber = index + 1;

      const currentYearData = monthlyData.find(
        item => item._id.year === currentYear && item._id.month === monthNumber
      );

      const previousYearData = monthlyData.find(
        item => item._id.year === previousYear && item._id.month === monthNumber
      );

      return {
        month: monthName,
        currentYear: currentYearData ? currentYearData.count : 0,
        previousYear: previousYearData ? previousYearData.count : 0
      };
    });

    // Format agent commission data for pie chart
    const pieChartData = agentCommissions.map(item => ({
      agentNPN: item._id,
      agentName: agentNameMap[item._id] || item._id,
      totalCommissions: item.totalCommissions,
      commissionCount: item.commissionCount
    }));

    res.json({
      success: true,
      data: {
        barChart: {
          labels: monthNames,
          currentYear: monthlyChartData.map(item => item.currentYear),
          previousYear: monthlyChartData.map(item => item.previousYear),
          currentYearLabel: `${currentYear} Contracts`,
          previousYearLabel: `${previousYear} Contracts`
        },
        pieChart: {
          labels: pieChartData.map(item => item.agentName),
          data: pieChartData.map(item => item.totalCommissions),
          agentDetails: pieChartData
        },
        metadata: {
          totalAgents: agentCommissions.length,
          totalCommissions: agentCommissions.reduce((sum, item) => sum + item.totalCommissions, 0),
          dataRange: {
            currentYear,
            previousYear
          }
        }
      }
    });

  } catch (error) {
    console.error('Error fetching chart data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chart data',
      message: error.message
    });
  }
});

export default router;
