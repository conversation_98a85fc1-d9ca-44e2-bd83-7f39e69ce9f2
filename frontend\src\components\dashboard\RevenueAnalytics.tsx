import { useState, useEffect } from 'react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import Card from '../ui/Card';
import { mockApi } from '../../utils/api';
import {
  formatCurrency,
  groupByCoverageMonth,
  calculateTotalCommission,
} from '../../utils/formatters';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

interface RevenueAnalyticsProps {
  npn?: string;
  isAgencyView?: boolean;
}

export default function RevenueAnalytics({ npn, isAgencyView = false }: RevenueAnalyticsProps) {
  const [commissionData, setCommissionData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewType, setViewType] = useState<'Weekly' | 'Monthly'>('Monthly');

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const data = await mockApi.getCommissionData(npn);
        setCommissionData(data);
      } catch (error) {
        console.error('Error fetching commission data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [npn]);

  // Prepare monthly bar chart data
  const monthlyData = groupByCoverageMonth(commissionData);
  const sortedMonths = Object.keys(monthlyData).sort();
  
  const barChartData = {
    labels: sortedMonths.map(month => {
      const date = new Date(month);
      return `${date.toLocaleString('default', { month: 'short' })}`;
    }),
    datasets: [
      {
        label: '2024 Weighted Premium',
        data: Object.entries(monthlyData).map(([_, items]) =>
          calculateTotalCommission(items)
        ),
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 1,
      },
      {
        label: '2023 Weighted Premium',
        data: Object.entries(monthlyData).map(([_, items]) =>
          calculateTotalCommission(items) * 0.85 // Mock previous year data
        ),
        backgroundColor: 'rgba(139, 92, 246, 0.8)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Prepare carrier distribution pie chart data
  const carrierData = commissionData.reduce((acc, item) => {
    const carrier = item['Carrier'] || item['Insurance Company'] || 'Unknown';
    acc[carrier] = (acc[carrier] || 0) + parseFloat(item['Amount'] || 0);
    return acc;
  }, {} as Record<string, number>);

  const pieChartData = {
    labels: Object.keys(carrierData),
    datasets: [
      {
        data: Object.values(carrierData),
        backgroundColor: [
          '#10B981', // Green
          '#F59E0B', // Orange  
          '#3B82F6', // Blue
          '#EF4444', // Red
          '#8B5CF6', // Purple
          '#06B6D4', // Cyan
          '#84CC16', // Lime
          '#F97316', // Orange-600
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.9)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
          callback: function(value: any) {
            return formatCurrency(value);
          }
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 11,
          },
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 15,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.9)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.raw / total) * 100).toFixed(1);
            return `${context.label}: ${formatCurrency(context.raw)} (${percentage}%)`;
          }
        }
      },
    },
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Revenue Analytics Bar Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Revenue Analytics</h3>
          <div className="flex rounded-md overflow-hidden">
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Weekly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Weekly')}
            >
              Weekly
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Monthly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Monthly')}
            >
              Monthly
            </button>
          </div>
        </div>
        <div className="h-80">
          <Bar data={barChartData} options={barChartOptions as any} />
        </div>
      </Card>

      {/* Carrier Distribution Pie Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Carrier Distribution</h3>
        </div>
        <div className="h-80">
          <Pie data={pieChartData} options={pieChartOptions as any} />
        </div>
      </Card>
    </div>
  );
}
