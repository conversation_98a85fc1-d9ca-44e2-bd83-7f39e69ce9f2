import { useState, useEffect } from 'react';
import { <PERSON>, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import Card from '../ui/Card';
import { formatCurrency } from '../../utils/formatters';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

interface RevenueAnalyticsProps {
  npn?: string;
  isAgencyView?: boolean;
  dashboardStats?: {
    totalAgents: number;
    totalContracts: number;
    totalCommissions: number;
    thisMonth: number;
    lastMonth: number;
  };
}

export default function RevenueAnalytics({ npn, isAgencyView = false, dashboardStats }: RevenueAnalyticsProps) {
  const [chartData, setChartData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [viewType, setViewType] = useState<'Weekly' | 'Monthly'>('Monthly');

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const { getRevenueAnalyticsData } = await import('../../utils/backendApi');

        console.log(' Calling getRevenueAnalyticsData directly...');
        const data = await getRevenueAnalyticsData(npn);
        console.log('📊 Chart data received:', data);
        setChartData(data);
      } catch (error) {
        console.error('Error fetching chart data:', error);
        setChartData(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [npn]);

  // Return early if loading or no chart data is available
  if (isLoading || !chartData) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-gray-400">
              {isLoading ? 'Loading chart data...' : 'No chart data available'}
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-gray-400">
              {isLoading ? 'Loading chart data...' : 'No chart data available'}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Create chart data structures from API data
  console.log('📊 Processing chart data:', chartData);

  const barChartData = {
    labels: chartData?.barChart?.labels || [],
    datasets: [
      {
        label: chartData?.barChart?.currentYearLabel || '2024 Contracts',
        data: chartData?.barChart?.currentYear || [],
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
      {
        label: chartData?.barChart?.previousYearLabel || '2023 Contracts',
        data: chartData?.barChart?.previousYear || [],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };

  console.log('📊 Bar chart data:', barChartData);
  console.log('📊 Bar chart labels length:', barChartData.labels.length);
  console.log('📊 Bar chart current year data:', barChartData.datasets[0].data);

  const pieChartData = {
    labels: chartData?.pieChart?.labels || [],
    datasets: [
      {
        data: chartData?.pieChart?.data || [],
        backgroundColor: [
          '#10B981', // Green
          '#F59E0B', // Orange
          '#3B82F6', // Blue
          '#EF4444', // Red
          '#8B5CF6', // Purple
          '#06B6D4', // Cyan
          '#84CC16', // Lime
          '#F97316', // Orange-600
          '#EC4899', // Pink
          '#14B8A6', // Teal
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
        hoverBorderWidth: 3,
      },
    ],
  };

  const pieTotal = chartData?.pieChart?.data?.reduce((sum: number, value: number) => sum + value, 0) || 0;

  console.log('📊 Pie chart data:', pieChartData);
  console.log('📊 Pie chart labels:', pieChartData.labels);
  console.log('📊 Pie chart data values:', pieChartData.datasets[0].data);
  console.log('📊 Pie total:', pieTotal);

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
          callback: function(value: any) {
            return formatCurrency(value);
          }
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 11,
          },
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 15,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / pieTotal) * 100).toFixed(1);
                return {
                  text: `${label} (${percentage}%)`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor,
                  lineWidth: data.datasets[0].borderWidth,
                  hidden: false,
                  index: i
                };
              });
            }
            return [];
          }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const percentage = ((context.raw / pieTotal) * 100).toFixed(1);
            return `${context.label}: ${formatCurrency(context.raw)} (${percentage}%)`;
          }
        }
      },
    },
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Revenue Analytics Bar Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Revenue Analytics</h3>
          <div className="flex rounded-md overflow-hidden">
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Weekly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Weekly')}
            >
              Weekly
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Monthly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Monthly')}
            >
              Monthly
            </button>
          </div>
        </div>
        <div className="h-80">
          {barChartData.labels.length > 0 ? (
            <Bar data={barChartData} options={barChartOptions as any} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-400">
              No bar chart data available
            </div>
          )}
        </div>
      </Card>

      {/* Carrier Distribution Pie Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Agent Commission Distribution</h3>
          <div className="text-sm text-gray-400">
            Total: {formatCurrency(pieTotal)}
          </div>
        </div>
        <div className="h-80">
          {pieChartData.labels.length > 0 ? (
            <Pie data={pieChartData} options={pieChartOptions as any} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-400">
              No pie chart data available
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
