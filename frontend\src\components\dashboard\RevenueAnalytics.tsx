import { useState, useEffect } from 'react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import Card from '../ui/Card';
import { mockApi } from '../../utils/api';
import { formatCurrency } from '../../utils/formatters';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

interface RevenueAnalyticsProps {
  npn?: string;
  isAgencyView?: boolean;
}

export default function RevenueAnalytics({ npn, isAgencyView = false }: RevenueAnalyticsProps) {
  const [commissionData, setCommissionData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewType, setViewType] = useState<'Weekly' | 'Monthly'>('Monthly');

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const data = await mockApi.getCommissionData(npn);
        setCommissionData(data);
      } catch (error) {
        console.error('Error fetching commission data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [npn]);

  // Process data for monthly bar chart
  const processMonthlyData = () => {
    const monthlyTotals: { [key: string]: number } = {};
    const monthlyTotalsPrevYear: { [key: string]: number } = {};

    commissionData.forEach(item => {
      const coverageMonth = item['Coverage Month'];
      const amount = parseFloat(item['Amount'] || '0');

      if (coverageMonth) {
        const date = new Date(coverageMonth);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        const currentYear = new Date().getFullYear();

        if (date.getFullYear() === currentYear) {
          monthlyTotals[monthKey] = (monthlyTotals[monthKey] || 0) + amount;
        } else if (date.getFullYear() === currentYear - 1) {
          monthlyTotalsPrevYear[monthKey] = (monthlyTotalsPrevYear[monthKey] || 0) + amount;
        }
      }
    });

    // Create labels for the last 12 months
    const months = [];
    const currentDate = new Date();
    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push(`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`);
    }

    return {
      labels: months.map(month => {
        const [year, monthNum] = month.split('-');
        const date = new Date(parseInt(year), parseInt(monthNum) - 1);
        return date.toLocaleDateString('en-US', { month: 'short' });
      }),
      currentYearData: months.map(month => monthlyTotals[month] || 0),
      prevYearData: months.map(month => {
        const [year, monthNum] = month.split('-');
        const prevYearMonth = `${parseInt(year) - 1}-${monthNum}`;
        return monthlyTotalsPrevYear[prevYearMonth] || 0;
      })
    };
  };

  const monthlyData = processMonthlyData();

  const barChartData = {
    labels: monthlyData.labels,
    datasets: [
      {
        label: '2024 Weighted Premium',
        data: monthlyData.currentYearData,
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
      {
        label: '2023 Weighted Premium',
        data: monthlyData.prevYearData,
        backgroundColor: 'rgba(139, 92, 246, 0.8)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };

  // Process data for carrier pie chart
  const processCarrierData = () => {
    const carrierTotals: { [key: string]: number } = {};

    commissionData.forEach(item => {
      const carrier = item['Carrier'] || 'Unknown';
      const amount = parseFloat(item['Amount'] || '0');
      carrierTotals[carrier] = (carrierTotals[carrier] || 0) + amount;
    });

    // Sort carriers by total amount and get top carriers
    const sortedCarriers = Object.entries(carrierTotals)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8); // Top 8 carriers

    return {
      labels: sortedCarriers.map(([carrier]) => carrier),
      data: sortedCarriers.map(([, amount]) => amount),
      total: Object.values(carrierTotals).reduce((sum, amount) => sum + amount, 0)
    };
  };

  const carrierData = processCarrierData();

  const pieChartData = {
    labels: carrierData.labels,
    datasets: [
      {
        data: carrierData.data,
        backgroundColor: [
          '#10B981', // Green - Ambetter
          '#F59E0B', // Orange - Oscar Health
          '#3B82F6', // Blue - Blue Cross Blue Shield
          '#EF4444', // Red - Aetna
          '#8B5CF6', // Purple - Cigna
          '#06B6D4', // Cyan - Humana
          '#84CC16', // Lime - UnitedHealth
          '#F97316', // Orange-600 - Others
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
        hoverBorderWidth: 3,
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 12,
          },
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
          callback: function(value: any) {
            return formatCurrency(value);
          }
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Poppins',
          },
        },
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'bottom' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.8)',
          font: {
            family: 'Poppins',
            size: 11,
          },
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 15,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / carrierData.total) * 100).toFixed(1);
                return {
                  text: `${label} (${percentage}%)`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor,
                  lineWidth: data.datasets[0].borderWidth,
                  hidden: false,
                  index: i
                };
              });
            }
            return [];
          }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.95)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: function(context: any) {
            const percentage = ((context.raw / carrierData.total) * 100).toFixed(1);
            return `${context.label}: ${formatCurrency(context.raw)} (${percentage}%)`;
          }
        }
      },
    },
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-700 rounded mb-4"></div>
            <div className="h-64 bg-gray-700 rounded"></div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Revenue Analytics Bar Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Revenue Analytics</h3>
          <div className="flex rounded-md overflow-hidden">
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Weekly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Weekly')}
            >
              Weekly
            </button>
            <button
              className={`px-3 py-1 text-xs font-medium ${
                viewType === 'Monthly'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } transition-colors duration-150`}
              onClick={() => setViewType('Monthly')}
            >
              Monthly
            </button>
          </div>
        </div>
        <div className="h-80">
          <Bar data={barChartData} options={barChartOptions as any} />
        </div>
      </Card>

      {/* Carrier Distribution Pie Chart */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Carrier Distribution</h3>
          <div className="text-sm text-gray-400">
            Total: {formatCurrency(carrierData.total)}
          </div>
        </div>
        <div className="h-80">
          <Pie data={pieChartData} options={pieChartOptions as any} />
        </div>
      </Card>
    </div>
  );
}
