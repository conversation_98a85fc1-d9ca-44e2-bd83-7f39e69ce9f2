import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import ContractingStatus from '../components/dashboard/ContractingStatus';
import CommissionData from '../components/dashboard/CommissionData';
import RevenueAnalytics from '../components/dashboard/RevenueAnalytics';
import AgencyViewToggle from '../components/dashboard/AgencyViewToggle';
import Card from '../components/ui/Card';
import { mockApi } from '../utils/api';
import { getCurrentTimestamp } from '../utils/formatters';

export default function AgentDashboard() {
  const [searchParams] = useSearchParams();
  const npn = searchParams.get('npn') || undefined;
  const [isAgencyView, setIsAgencyView] = useState(false);
  const [agentInfo, setAgentInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(getCurrentTimestamp());

  useEffect(() => {
    async function fetchAgentInfo() {
      if (!npn) {
        setAgentInfo(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        // In a real app, we would fetch agent info from the API
        // For now, we'll use the first item from contracting data
        const contractingData = await mockApi.getContractingData(npn);
        if (contractingData.length > 0) {
          setAgentInfo({
            firstName: contractingData[0]['First Name'],
            lastName: contractingData[0]['Last Name'],
            npn: contractingData[0]['Agent NPN'],
            upline: contractingData[0]['Upline'],
          });
        }
        setLastUpdated(getCurrentTimestamp());
      } catch (error) {
        console.error('Error fetching agent info:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchAgentInfo();
  }, [npn]);

  const handleAgencyViewToggle = (value: boolean) => {
    setIsAgencyView(value);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">
            {isLoading ? (
              <div className="h-8 w-48 bg-gray-700 rounded animate-pulse"></div>
            ) : agentInfo ? (
              `${agentInfo.firstName} ${agentInfo.lastName}'s Dashboard`
            ) : (
              'Agent Dashboard'
            )}
          </h1>
          <p className="text-gray-400 mt-1">
            {npn ? `NPN: ${npn}` : 'No agent selected'}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <AgencyViewToggle 
            npn={npn} 
            isAgencyView={isAgencyView} 
            onToggle={handleAgencyViewToggle} 
          />
        </div>
      </div>

      {!npn ? (
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-white mb-4">No Agent Selected</h2>
          <p className="text-gray-400 mb-6">
            Please provide an NPN parameter in the URL to view agent data.
          </p>
          <p className="text-gray-400">
            Example: <code className="bg-gray-800 px-2 py-1 rounded">?npn=11206123</code>
          </p>
        </Card>
      ) : (
        <div className="space-y-8">
          <RevenueAnalytics npn={npn} isAgencyView={isAgencyView} />
          <ContractingStatus npn={npn} isAgencyView={isAgencyView} />
          <CommissionData npn={npn} isAgencyView={isAgencyView} />
        </div>
      )}

      <div className="text-xs text-gray-500 text-right">
        Data last updated: {lastUpdated}
      </div>
    </div>
  );
}
